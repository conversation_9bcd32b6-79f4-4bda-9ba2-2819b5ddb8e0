# Oh My Zsh Plugins for Website Monitor Development

This guide will help you install the recommended Oh My Zsh plugins to make working with your Website Monitor project much easier from the command line.

## 🔍 Current Setup Detection

Your current Oh My Zsh configuration:
- **Theme**: robby<PERSON><PERSON>
- **Current plugins**: git
- **Oh My Zsh path**: ~/.oh-my-zsh

## 🚀 Recommended Plugins for Your Project

### Essential Plugins (Built-in)

These plugins come with Oh My Zsh and just need to be enabled:

1. **git** ✅ (already enabled)
2. **npm** - NPM command completions and shortcuts
3. **node** - Node.js version management and shortcuts  
4. **docker** - Docker command completions
5. **docker-compose** - Docker Compose completions
6. **systemd** - Systemd service management shortcuts
7. **ssh-agent** - SSH key management
8. **history** - Enhanced history search
9. **colored-man-pages** - Colorized man pages
10. **command-not-found** - Suggests packages for missing commands

### Advanced Plugins (Need Installation)

These provide powerful enhancements:

1. **zsh-autosuggestions** - Fish-like autosuggestions
2. **zsh-syntax-highlighting** - Syntax highlighting for commands
3. **zsh-completions** - Additional completions
4. **fzf** - Fuzzy finder for files and history

## 📦 Installation Steps

### Step 1: Update Your Plugin List

```bash
# Backup your current .zshrc
cp ~/.zshrc ~/.zshrc.backup

# Edit your .zshrc file
nano ~/.zshrc
```

Find the plugins line (around line 73) and replace:
```bash
plugins=(git)
```

With:
```bash
plugins=(
  git
  npm
  node
  docker
  docker-compose
  systemd
  ssh-agent
  history
  colored-man-pages
  command-not-found
)
```

### Step 2: Install Advanced Plugins

#### Install zsh-autosuggestions
```bash
git clone https://github.com/zsh-users/zsh-autosuggestions ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions
```

#### Install zsh-syntax-highlighting
```bash
git clone https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting
```

#### Install zsh-completions
```bash
git clone https://github.com/zsh-users/zsh-completions ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-completions
```

#### Install fzf (Fuzzy Finder)
```bash
# Install fzf via Homebrew (macOS)
brew install fzf

# Install fzf key bindings and fuzzy completion
$(brew --prefix)/opt/fzf/install
```

### Step 3: Update Plugin Configuration

Add the advanced plugins to your .zshrc:
```bash
plugins=(
  git
  npm
  node
  docker
  docker-compose
  systemd
  ssh-agent
  history
  colored-man-pages
  command-not-found
  zsh-autosuggestions
  zsh-syntax-highlighting
  zsh-completions
  fzf
)
```

### Step 4: Apply Changes

```bash
# Reload your shell configuration
source ~/.zshrc

# Or restart your terminal
```

## 🎯 Plugin Benefits for Your Project

### NPM Plugin Benefits
- **npm run** → **nr** (shortcut)
- **npm install** → **ni** (shortcut)
- **npm test** → **nt** (shortcut)
- Tab completion for npm scripts

### Node Plugin Benefits
- **node_modules/.bin** automatically added to PATH
- Version detection and display
- Quick access to Node.js documentation

### Docker Plugin Benefits
- Tab completion for Docker commands
- Shortcuts like **dps** for `docker ps`
- **dex** for `docker exec -it`

### Git Plugin Benefits (already enabled)
- **gst** for `git status`
- **ga** for `git add`
- **gcm** for `git commit -m`
- **gp** for `git push`

### SSH-Agent Plugin Benefits
- Automatically starts ssh-agent
- Loads your SSH keys
- Perfect for server deployments

## 🔧 Useful Aliases for Your Project

Add these custom aliases to your .zshrc (after the plugins section):

```bash
# Website Monitor Project Aliases
alias wm-start="./start-daemon.sh start"
alias wm-stop="./start-daemon.sh stop"
alias wm-status="./start-daemon.sh status"
alias wm-logs="./start-daemon.sh logs"
alias wm-restart="./start-daemon.sh restart"
alias wm-deploy="./deploy.sh"
alias wm-config="npm run check-config"
alias wm-test="npm run test-headless"

# Docker shortcuts for your project
alias wm-docker-up="docker-compose up -d"
alias wm-docker-down="docker-compose down"
alias wm-docker-logs="docker-compose logs -f"
alias wm-docker-restart="docker-compose restart"

# Server deployment shortcuts
alias deploy-to-server="scp -r . user@your-server:/path/to/website-monitor/"
alias connect-server="ssh user@your-server"

# Development shortcuts
alias ll="ls -la"
alias la="ls -la"
alias ..="cd .."
alias ...="cd ../.."
alias grep="grep --color=auto"
```

## 🎨 Enhanced Theme Recommendation

Consider upgrading to a more informative theme. Add this to your .zshrc:

```bash
# Change from robbyrussell to a more informative theme
ZSH_THEME="agnoster"  # Shows git status, path, and more
# or
ZSH_THEME="powerlevel10k/powerlevel10k"  # Highly customizable (needs installation)
```

### Install Powerlevel10k (Optional but Recommended)
```bash
git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/themes/powerlevel10k
```

Then set `ZSH_THEME="powerlevel10k/powerlevel10k"` in your .zshrc.

## 🚀 Quick Setup Script

Here's a one-liner to set up everything:

```bash
# Run this command to automatically set up recommended plugins
curl -fsSL https://raw.githubusercontent.com/your-repo/setup-zsh.sh | bash
```

Or manually run these commands:

```bash
# Install advanced plugins
git clone https://github.com/zsh-users/zsh-autosuggestions ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions
git clone https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting
git clone https://github.com/zsh-users/zsh-completions ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-completions

# Install fzf
brew install fzf
$(brew --prefix)/opt/fzf/install

# Backup and update .zshrc
cp ~/.zshrc ~/.zshrc.backup
```

## ✅ Verification

After setup, test these commands:

```bash
# Test npm completions
npm run <TAB><TAB>

# Test docker completions  
docker <TAB><TAB>

# Test git shortcuts
gst  # Should show git status

# Test your project aliases
wm-status  # Should check website monitor status

# Test autosuggestions (type a previous command partially)
npm i<TAB>  # Should suggest npm install

# Test fuzzy finder
Ctrl+R  # Should open enhanced history search
```

## 🎯 Expected Benefits

After installation, you'll have:

- ✅ **Faster command typing** with autosuggestions
- ✅ **Syntax highlighting** to catch errors before running
- ✅ **Tab completion** for npm, docker, git commands
- ✅ **Quick shortcuts** for common tasks
- ✅ **Enhanced history search** with fzf
- ✅ **Colorized output** for better readability
- ✅ **Project-specific aliases** for website monitor management

This setup will make deploying and managing your Website Monitor much more efficient!
