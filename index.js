#!/usr/bin/env node

require("dotenv").config();
const fs = require("fs").promises;
const cron = require("node-cron");
const WebsiteMonitor = require("./src/monitor");
const BrowserMonitor = require("./src/browser-monitor");
const logger = require("./src/logger");

class NewsletterApp {
  constructor() {
    this.configFile = "./urls.json";
    this.slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;
    this.checkInterval = parseInt(process.env.CHECK_INTERVAL_MINUTES) || 30;
    this.monitor = null;
    this.browserMonitor = null;
    this.useBrowser = process.env.USE_BROWSER === "true" || false;

    this.validateConfig();
  }

  validateConfig() {
    if (!this.slackWebhookUrl) {
      console.error("❌ SLACK_WEBHOOK_URL environment variable is required");
      console.log(
        "Please copy .env.example to .env and configure your Slack webhook URL"
      );
      process.exit(1);
    }

    if (this.checkInterval < 1) {
      console.error("❌ CHECK_INTERVAL_MINUTES must be at least 1 minute");
      process.exit(1);
    }
  }

  async loadConfig() {
    try {
      const configData = await fs.readFile(this.configFile, "utf8");
      const config = JSON.parse(configData);

      if (!config.site || !Array.isArray(config.site)) {
        throw new Error('Configuration must have a "site" array');
      }

      if (config.site.length === 0) {
        throw new Error("No sites configured in urls.json");
      }

      // Validate each site configuration
      for (const site of config.site) {
        if (!site.name || !site.url) {
          throw new Error('Each site must have "name" and "url" properties');
        }
      }

      return config.site;
    } catch (error) {
      console.error("❌ Error loading configuration:", error.message);
      process.exit(1);
    }
  }

  async runCheck() {
    try {
      const sites = await this.loadConfig();
      await this.monitor.checkAllSites(sites);
    } catch (error) {
      logger.failure("Error during check", {
        error: error.message,
        stack: error.stack,
      });
    }
  }

  async start() {
    logger.startup("Starting Newsletter Monitor...");
    logger.notification("Slack notifications enabled");
    logger.schedule(`Check interval: ${this.checkInterval} minutes`);

    // Initialize the appropriate monitor
    if (this.useBrowser) {
      logger.browser(
        "Using browser-based monitoring (slower but more reliable)"
      );
      this.browserMonitor = new BrowserMonitor(this.slackWebhookUrl);
      this.monitor = this.browserMonitor;
    } else {
      logger.info("⚡ Using fast HTTP-based monitoring");
      this.monitor = new WebsiteMonitor(this.slackWebhookUrl);
    }

    // Load and validate configuration first
    const sites = await this.loadConfig();

    // Send startup notification with sites list
    await this.monitor.slackNotifier.sendStartupNotification(sites);
    logger.config(`Monitoring ${sites.length} sites:`);
    sites.forEach((site) => {
      logger.config(`   • ${site.name}: ${site.url}`);
    });

    // Run initial check
    logger.monitoring("Running initial check...");
    await this.runCheck();

    // Schedule periodic checks
    const cronExpression = `*/${this.checkInterval} * * * *`;
    logger.schedule(`Scheduling checks every ${this.checkInterval} minutes`);

    cron.schedule(cronExpression, async () => {
      logger.schedule(`Scheduled check at ${new Date().toLocaleString()}`);
      await this.runCheck();
    });

    logger.success("Newsletter Monitor is running! Press Ctrl+C to stop.");
  }

  async stop() {
    logger.shutdown("Stopping Newsletter Monitor...");
    process.exit(0);
  }
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
  logger.shutdown("Received SIGINT, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", async () => {
  logger.shutdown("Received SIGTERM, shutting down gracefully...");
  process.exit(0);
});

// Start the application
if (require.main === module) {
  const app = new NewsletterApp();
  app.start().catch((error) => {
    logger.failure("Failed to start application", {
      error: error.message,
      stack: error.stack,
    });
    process.exit(1);
  });
}

module.exports = NewsletterApp;
