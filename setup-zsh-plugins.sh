#!/bin/bash

# Oh My Zsh Plugin Setup Script for Website Monitor Project
# This script automatically installs and configures recommended plugins

set -e

echo "🚀 Setting up Oh My Zsh plugins for Website Monitor development..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Oh My Zsh is installed
if [ ! -d "$HOME/.oh-my-zsh" ]; then
    echo -e "${RED}❌ Oh My Zsh is not installed. Please install it first:${NC}"
    echo "sh -c \"\$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)\""
    exit 1
fi

echo -e "${GREEN}✅ Oh My Zsh detected${NC}"

# Backup current .zshrc
echo -e "${BLUE}📋 Backing up current .zshrc...${NC}"
cp ~/.zshrc ~/.zshrc.backup.$(date +%Y%m%d_%H%M%S)
echo -e "${GREEN}✅ Backup created${NC}"

# Install advanced plugins
echo -e "${BLUE}📦 Installing advanced plugins...${NC}"

# zsh-autosuggestions
if [ ! -d "${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/plugins/zsh-autosuggestions" ]; then
    echo "Installing zsh-autosuggestions..."
    git clone https://github.com/zsh-users/zsh-autosuggestions ${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/plugins/zsh-autosuggestions
    echo -e "${GREEN}✅ zsh-autosuggestions installed${NC}"
else
    echo -e "${YELLOW}⚠️ zsh-autosuggestions already installed${NC}"
fi

# zsh-syntax-highlighting
if [ ! -d "${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting" ]; then
    echo "Installing zsh-syntax-highlighting..."
    git clone https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting
    echo -e "${GREEN}✅ zsh-syntax-highlighting installed${NC}"
else
    echo -e "${YELLOW}⚠️ zsh-syntax-highlighting already installed${NC}"
fi

# zsh-completions
if [ ! -d "${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/plugins/zsh-completions" ]; then
    echo "Installing zsh-completions..."
    git clone https://github.com/zsh-users/zsh-completions ${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/plugins/zsh-completions
    echo -e "${GREEN}✅ zsh-completions installed${NC}"
else
    echo -e "${YELLOW}⚠️ zsh-completions already installed${NC}"
fi

# Install fzf if not present
if ! command -v fzf &> /dev/null; then
    echo -e "${BLUE}📦 Installing fzf...${NC}"
    if command -v brew &> /dev/null; then
        brew install fzf
        $(brew --prefix)/opt/fzf/install --key-bindings --completion --no-update-rc
        echo -e "${GREEN}✅ fzf installed via Homebrew${NC}"
    else
        echo -e "${YELLOW}⚠️ Homebrew not found. Please install fzf manually:${NC}"
        echo "git clone --depth 1 https://github.com/junegunn/fzf.git ~/.fzf"
        echo "~/.fzf/install"
    fi
else
    echo -e "${GREEN}✅ fzf already installed${NC}"
fi

# Update .zshrc with new plugins
echo -e "${BLUE}⚙️ Updating .zshrc configuration...${NC}"

# Create new plugins configuration
NEW_PLUGINS="plugins=(
  git
  npm
  node
  docker
  docker-compose
  systemd
  ssh-agent
  history
  colored-man-pages
  command-not-found
  zsh-autosuggestions
  zsh-syntax-highlighting
  zsh-completions
  fzf
)"

# Replace the plugins line in .zshrc
if grep -q "plugins=(" ~/.zshrc; then
    # Create a temporary file with the new configuration
    awk -v new_plugins="$NEW_PLUGINS" '
    /^plugins=\(/ {
        print new_plugins
        # Skip until we find the closing parenthesis
        while (getline > 0 && !/^\)/ && !/^plugins=.*\)/) {
            # Skip lines until closing parenthesis
        }
        next
    }
    { print }
    ' ~/.zshrc > ~/.zshrc.tmp && mv ~/.zshrc.tmp ~/.zshrc
    echo -e "${GREEN}✅ Plugins configuration updated${NC}"
else
    echo -e "${RED}❌ Could not find plugins configuration in .zshrc${NC}"
    exit 1
fi

# Add custom aliases for Website Monitor
echo -e "${BLUE}🔧 Adding Website Monitor aliases...${NC}"

ALIASES="
# Website Monitor Project Aliases (added by setup script)
alias wm-start=\"./start-daemon.sh start\"
alias wm-stop=\"./start-daemon.sh stop\"
alias wm-status=\"./start-daemon.sh status\"
alias wm-logs=\"./start-daemon.sh logs\"
alias wm-restart=\"./start-daemon.sh restart\"
alias wm-deploy=\"./deploy.sh\"
alias wm-config=\"npm run check-config\"
alias wm-test=\"npm run test-headless\"

# Docker shortcuts for Website Monitor
alias wm-docker-up=\"docker-compose up -d\"
alias wm-docker-down=\"docker-compose down\"
alias wm-docker-logs=\"docker-compose logs -f\"
alias wm-docker-restart=\"docker-compose restart\"

# Development shortcuts
alias ll=\"ls -la\"
alias la=\"ls -la\"
alias ..=\"cd ..\"
alias ...=\"cd ../..\"
alias grep=\"grep --color=auto\"
"

# Check if aliases already exist
if ! grep -q "Website Monitor Project Aliases" ~/.zshrc; then
    echo "$ALIASES" >> ~/.zshrc
    echo -e "${GREEN}✅ Website Monitor aliases added${NC}"
else
    echo -e "${YELLOW}⚠️ Website Monitor aliases already exist${NC}"
fi

# Optional: Suggest theme upgrade
echo -e "${BLUE}🎨 Theme recommendation:${NC}"
echo "Consider upgrading to a more informative theme like 'agnoster' or 'powerlevel10k'"
echo "Current theme: $(grep '^ZSH_THEME=' ~/.zshrc | cut -d'=' -f2 | tr -d '\"')"

read -p "Would you like to install Powerlevel10k theme? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ ! -d "${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/themes/powerlevel10k" ]; then
        echo "Installing Powerlevel10k..."
        git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/themes/powerlevel10k
        
        # Update theme in .zshrc
        sed -i.bak 's/^ZSH_THEME=.*/ZSH_THEME="powerlevel10k\/powerlevel10k"/' ~/.zshrc
        echo -e "${GREEN}✅ Powerlevel10k installed and configured${NC}"
        echo -e "${YELLOW}⚠️ You'll be prompted to configure it when you restart your terminal${NC}"
    else
        echo -e "${YELLOW}⚠️ Powerlevel10k already installed${NC}"
    fi
fi

echo -e "${GREEN}🎉 Setup complete!${NC}"
echo
echo -e "${BLUE}📋 Summary of changes:${NC}"
echo "✅ Installed zsh-autosuggestions, zsh-syntax-highlighting, zsh-completions"
echo "✅ Configured fzf fuzzy finder"
echo "✅ Updated plugins in .zshrc"
echo "✅ Added Website Monitor aliases"
echo "✅ Backup created: ~/.zshrc.backup.*"
echo
echo -e "${YELLOW}🔄 To apply changes, run:${NC}"
echo "source ~/.zshrc"
echo
echo -e "${BLUE}🧪 Test your new setup:${NC}"
echo "• Type 'wm-' and press TAB to see Website Monitor aliases"
echo "• Type 'npm ' and press TAB to see completions"
echo "• Press Ctrl+R for enhanced history search"
echo "• Start typing a previous command to see autosuggestions"
echo
echo -e "${GREEN}Happy coding! 🚀${NC}"
