# Logging System Documentation

This application uses a comprehensive logging system that outputs to both console and files, with daily file rotation for easy log tracking and management.

## Overview

The logging system is built using [<PERSON>](https://github.com/winstonjs/winston) with daily rotating file transport. It provides:

- **Console output**: Colored, formatted logs for development and monitoring
- **File output**: Structured JSON logs for analysis and debugging
- **Daily rotation**: Automatic log file rotation by date
- **Automatic cleanup**: Old logs are compressed and deleted after 30 days
- **Error separation**: Separate files for error-level logs
- **Exception handling**: Automatic logging of uncaught exceptions and promise rejections

## Log Files

All log files are stored in the `logs/` directory:

### Daily Application Logs
- **Pattern**: `app-YYYY-MM-DD.log`
- **Content**: All log levels (info, warn, error, debug)
- **Format**: JSON with timestamp, level, message, and metadata
- **Example**: `logs/app-2025-06-22.log`

### Daily Error Logs
- **Pattern**: `error-YYYY-MM-DD.log`
- **Content**: Only error-level logs
- **Format**: JSON with timestamp, level, message, error details, and stack traces
- **Example**: `logs/error-2025-06-22.log`

### Exception Logs
- **Pattern**: `exceptions-YYYY-MM-DD.log`
- **Content**: Uncaught exceptions
- **Format**: JSON with full stack traces

### Rejection Logs
- **Pattern**: `rejections-YYYY-MM-DD.log`
- **Content**: Unhandled promise rejections
- **Format**: JSON with full stack traces

## Log Levels

The system supports standard log levels (configurable via `LOG_LEVEL` environment variable):

- `error`: Error conditions
- `warn`: Warning conditions
- `info`: Informational messages (default)
- `debug`: Debug-level messages

## Configuration

### Environment Variables

```bash
# Set log level (debug, info, warn, error)
LOG_LEVEL=info
```

### File Rotation Settings

- **Max file size**: 20MB per file
- **Retention period**: 30 days
- **Compression**: Old files are automatically gzipped
- **Archive naming**: `app-YYYY-MM-DD.log.gz`

## Usage Examples

### Basic Logging

```javascript
const logger = require('./src/logger');

logger.info('Application started');
logger.warn('This is a warning');
logger.error('An error occurred');
logger.debug('Debug information');
```

### Logging with Metadata

```javascript
logger.info('User logged in', { 
    userId: 12345, 
    timestamp: new Date().toISOString() 
});

logger.error('Database connection failed', {
    error: error.message,
    stack: error.stack,
    database: 'primary'
});
```

### Helper Methods

The logger provides convenient helper methods with emojis:

```javascript
logger.startup('Application starting up');        // 🚀
logger.success('Operation completed');             // ✅
logger.warning('Warning message');                 // ⚠️
logger.failure('Operation failed');                // ❌
logger.monitoring('Monitoring activity');          // 🔍
logger.notification('Notification sent');          // 📧
logger.schedule('Scheduled task executed');        // ⏰
logger.browser('Browser operation completed');     // 🌐
logger.config('Configuration loaded');             // 📋
logger.shutdown('Application shutting down');      // 🛑
```

## Viewing Logs

### Using the Daemon Script

```bash
# View today's application logs (live tail)
./start-daemon.sh logs

# View today's error logs (live tail)
./start-daemon.sh errors

# List all available log files
./start-daemon.sh list-logs
```

### Manual Log Viewing

```bash
# View today's application logs
tail -f logs/app-$(date +%Y-%m-%d).log

# View today's error logs
tail -f logs/error-$(date +%Y-%m-%d).log

# View all log files
ls -la logs/

# View compressed archived logs
zcat logs/app-2025-06-21.log.gz | head -20
```

### Parsing JSON Logs

Since file logs are in JSON format, you can use tools like `jq` for analysis:

```bash
# Filter error logs
cat logs/app-2025-06-22.log | jq 'select(.level=="error")'

# Extract messages only
cat logs/app-2025-06-22.log | jq -r '.message'

# Filter by timestamp
cat logs/app-2025-06-22.log | jq 'select(.timestamp > "2025-06-22 14:00:00")'

# Count log levels
cat logs/app-2025-06-22.log | jq -r '.level' | sort | uniq -c
```

## Log Rotation Events

The system logs its own rotation events:

```json
{"level":"info","message":"📁 Log file rotated","oldFilename":"app-2025-06-21.log","newFilename":"app-2025-06-22.log","timestamp":"2025-06-22 00:00:00"}
{"level":"info","message":"📦 Log file archived","zipFilename":"app-2025-06-21.log.gz","timestamp":"2025-06-22 00:00:01"}
{"level":"info","message":"🗑️ Old log file removed","removedFilename":"app-2025-05-23.log.gz","timestamp":"2025-06-22 00:00:02"}
```

## Troubleshooting

### No Log Files Created

1. Check if the `logs/` directory exists and is writable
2. Verify the application has permission to write files
3. Check for any startup errors in the console

### Large Log Files

1. Adjust the `maxSize` setting in `src/logger.js`
2. Reduce the log level (e.g., from `debug` to `info`)
3. Check if the rotation is working properly

### Missing Logs

1. Verify the `LOG_LEVEL` environment variable
2. Check if logs are being written to the correct date file
3. Ensure the application is using the logger correctly

## Performance Considerations

- JSON formatting adds minimal overhead
- File I/O is asynchronous and non-blocking
- Log rotation happens automatically without service interruption
- Compressed archives save significant disk space

## Integration with Monitoring

The structured JSON format makes it easy to integrate with log aggregation systems:

- **ELK Stack**: Elasticsearch, Logstash, Kibana
- **Splunk**: Direct JSON ingestion
- **Fluentd**: JSON parsing and forwarding
- **Grafana Loki**: Log aggregation and visualization

## Best Practices

1. Use appropriate log levels
2. Include relevant metadata with log messages
3. Avoid logging sensitive information (passwords, tokens)
4. Use the helper methods for consistent formatting
5. Monitor log file sizes and rotation
6. Set up log monitoring and alerting for production
