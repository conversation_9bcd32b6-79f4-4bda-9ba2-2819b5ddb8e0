const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const fs = require('fs');

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output (with colors and emojis)
const consoleFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.colorize({ all: true }),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
        const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
        return `${timestamp} [${level}] ${message} ${metaStr}`;
    })
);

// Custom format for file output (without colors, structured)
const fileFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.json()
);

// Daily rotate file transport for general logs
const dailyRotateFileTransport = new DailyRotateFile({
    filename: path.join(logsDir, 'app-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    zippedArchive: true,
    maxSize: '20m',
    maxFiles: '30d', // Keep logs for 30 days
    format: fileFormat
});

// Daily rotate file transport for error logs
const errorRotateFileTransport = new DailyRotateFile({
    filename: path.join(logsDir, 'error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    zippedArchive: true,
    maxSize: '20m',
    maxFiles: '30d', // Keep error logs for 30 days
    level: 'error',
    format: fileFormat
});

// Create the logger
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: fileFormat,
    transports: [
        // Console transport
        new winston.transports.Console({
            format: consoleFormat
        }),
        // Daily rotating file transport for all logs
        dailyRotateFileTransport,
        // Daily rotating file transport for errors only
        errorRotateFileTransport
    ],
    // Handle exceptions and rejections
    exceptionHandlers: [
        new winston.transports.Console({
            format: consoleFormat
        }),
        new DailyRotateFile({
            filename: path.join(logsDir, 'exceptions-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            zippedArchive: true,
            maxSize: '20m',
            maxFiles: '30d',
            format: fileFormat
        })
    ],
    rejectionHandlers: [
        new winston.transports.Console({
            format: consoleFormat
        }),
        new DailyRotateFile({
            filename: path.join(logsDir, 'rejections-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            zippedArchive: true,
            maxSize: '20m',
            maxFiles: '30d',
            format: fileFormat
        })
    ]
});

// Add event listeners for file rotation
dailyRotateFileTransport.on('rotate', (oldFilename, newFilename) => {
    logger.info('📁 Log file rotated', { oldFilename, newFilename });
});

errorRotateFileTransport.on('rotate', (oldFilename, newFilename) => {
    logger.info('📁 Error log file rotated', { oldFilename, newFilename });
});

// Add event listeners for file archiving
dailyRotateFileTransport.on('archive', (zipFilename) => {
    logger.info('📦 Log file archived', { zipFilename });
});

errorRotateFileTransport.on('archive', (zipFilename) => {
    logger.info('📦 Error log file archived', { zipFilename });
});

// Add event listeners for file deletion
dailyRotateFileTransport.on('logRemoved', (removedFilename) => {
    logger.info('🗑️ Old log file removed', { removedFilename });
});

errorRotateFileTransport.on('logRemoved', (removedFilename) => {
    logger.info('🗑️ Old error log file removed', { removedFilename });
});

// Helper methods for common logging patterns
logger.startup = (message, meta = {}) => {
    logger.info(`🚀 ${message}`, meta);
};

logger.success = (message, meta = {}) => {
    logger.info(`✅ ${message}`, meta);
};

logger.warning = (message, meta = {}) => {
    logger.warn(`⚠️ ${message}`, meta);
};

logger.failure = (message, meta = {}) => {
    logger.error(`❌ ${message}`, meta);
};

logger.monitoring = (message, meta = {}) => {
    logger.info(`🔍 ${message}`, meta);
};

logger.notification = (message, meta = {}) => {
    logger.info(`📧 ${message}`, meta);
};

logger.schedule = (message, meta = {}) => {
    logger.info(`⏰ ${message}`, meta);
};

logger.browser = (message, meta = {}) => {
    logger.info(`🌐 ${message}`, meta);
};

logger.config = (message, meta = {}) => {
    logger.info(`📋 ${message}`, meta);
};

logger.shutdown = (message, meta = {}) => {
    logger.info(`🛑 ${message}`, meta);
};

// Export the logger
module.exports = logger;
