const axios = require("axios");
const cheerio = require("cheerio");
const Storage = require("./storage");
const SlackNotifier = require("./slack");
const logger = require("./logger");

class WebsiteMonitor {
  constructor(slackWebhookUrl) {
    this.storage = new Storage();
    this.slackNotifier = new SlackNotifier(slackWebhookUrl);
    this.userAgent =
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
  }

  async fetchPage(url) {
    try {
      // Try multiple strategies to bypass anti-bot protection
      const strategies = [
        // Strategy 1: Full browser headers
        {
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            Accept:
              "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "en-US,en;q=0.9,vi;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            DNT: "1",
            Connection: "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0",
            "sec-ch-ua":
              '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',
          },
        },
        // Strategy 2: Mobile browser
        {
          headers: {
            "User-Agent":
              "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
            Accept:
              "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            Connection: "keep-alive",
            "Upgrade-Insecure-Requests": "1",
          },
        },
        // Strategy 3: Simple headers
        {
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            Accept:
              "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
          },
        },
      ];

      let lastError;

      for (let i = 0; i < strategies.length; i++) {
        try {
          console.log(`🔄 Trying strategy ${i + 1} for ${url}`);

          const response = await axios.get(url, {
            ...strategies[i],
            timeout: 30000,
            maxRedirects: 5,
            validateStatus: function (status) {
              return status >= 200 && status < 400; // Accept redirects
            },
          });

          console.log(`✅ Strategy ${i + 1} successful for ${url}`);
          return response.data;
        } catch (error) {
          lastError = error;
          console.log(
            `❌ Strategy ${i + 1} failed: ${
              error.response?.status || error.message
            }`
          );

          // Wait between attempts
          if (i < strategies.length - 1) {
            await new Promise((resolve) => setTimeout(resolve, 2000));
          }
        }
      }

      throw lastError;
    } catch (error) {
      const statusCode = error.response?.status;
      const errorMessage = error.response?.statusText || error.message;

      if (statusCode === 403) {
        throw new Error(
          `Access denied (403) - Website may be blocking automated requests: ${errorMessage}`
        );
      } else if (statusCode === 429) {
        throw new Error(
          `Rate limited (429) - Too many requests: ${errorMessage}`
        );
      } else if (statusCode >= 500) {
        throw new Error(
          `Server error (${statusCode}) - Website may be down: ${errorMessage}`
        );
      } else {
        throw new Error(`Failed to fetch ${url}: ${errorMessage}`);
      }
    }
  }

  extractItems(html, url, siteName) {
    const $ = cheerio.load(html);
    const items = [];

    // Get domain for source identification
    const domain = new URL(url).hostname;

    console.log(`🔍 Extracting items from ${siteName} (${domain})`);

    // Specific selectors for Batdongsan.com.vn
    if (domain.includes("batdongsan.com.vn")) {
      console.log("🏠 Using Batdongsan.com.vn specific selectors");

      // Main property listing cards
      const propertyCards = $(
        ".js__card.js__card-full-web.pr-container.re__card-full"
      );
      console.log(`   Found ${propertyCards.length} main property cards`);

      propertyCards.each((_, element) => {
        const $element = $(element);

        // Extract title from the main link
        const titleEl = $element.find(".pr-title.js__card-title");
        const title = titleEl.text().trim();

        // Extract URL from the main link
        const linkEl = $element
          .find("a.js__product-link-for-product-id")
          .first();
        let itemUrl = linkEl.attr("href") || "";
        if (itemUrl && !itemUrl.startsWith("http")) {
          itemUrl = `https://${domain}${itemUrl}`;
        }

        // Extract price
        const priceEl = $element.find(
          ".re__card-config-price.js__card-config-item"
        );
        const price = priceEl.text().trim() || "Giá liên hệ";

        // Extract image
        const imgEl = $element.find(".re__card-image img").first();
        let imageUrl = imgEl.attr("src") || imgEl.attr("data-src") || "";
        if (imageUrl && !imageUrl.startsWith("http")) {
          imageUrl = `https:${imageUrl}`;
        }

        // Extract area and other details
        const areaEl = $element.find(
          ".re__card-config-area.js__card-config-item"
        );
        const area = areaEl.text().trim();

        const bedroomEl = $element.find(
          ".re__card-config-bedroom.js__card-config-item span"
        );
        const bedroom = bedroomEl.text().trim();

        const locationEl = $element.find(".re__card-location span");
        const location = locationEl.text().trim();

        // Extract posting time
        const timeEl = $element.find(".re__card-published-info-published-at");
        let postedTime = timeEl.first().text().trim();
        // Clean up the posting time text (remove extra whitespace and duplicates)
        if (postedTime) {
          postedTime = postedTime.split("\n")[0].trim();
        }

        if (title && title.length > 10) {
          items.push({
            title: title.trim(),
            url: itemUrl,
            price: price,
            image: imageUrl,
            area: area,
            bedroom: bedroom ? `${bedroom} phòng ngủ` : "",
            location: location,
            postedTime: postedTime,
            source: siteName,
            domain: domain,
            extractedAt: new Date().toISOString(),
          });
        }
      });

      // Also check for compact cards (verified listings)
      const compactCards = $(
        ".js__card.js__card-compact-web.pr-container.re__card-compact"
      );
      console.log(`   Found ${compactCards.length} compact property cards`);

      compactCards.each((_, element) => {
        const $element = $(element);

        const titleEl = $element.find(".js__card-title");
        const title = titleEl.text().trim();

        const linkEl = $element
          .find("a.js__product-link-for-product-id")
          .first();
        let itemUrl = linkEl.attr("href") || "";
        if (itemUrl && !itemUrl.startsWith("http")) {
          itemUrl = `https://${domain}${itemUrl}`;
        }

        const priceEl = $element.find(".re__card-config-price");
        const price = priceEl.text().trim() || "Giá liên hệ";

        const imgEl = $element.find(".pr-img").first();
        let imageUrl = imgEl.attr("src") || imgEl.attr("data-src") || "";
        if (imageUrl && !imageUrl.startsWith("http")) {
          imageUrl = `https:${imageUrl}`;
        }

        const areaEl = $element.find(".re__card-config-area");
        const area = areaEl.text().replace("·", "").trim();

        const locationEl = $element.find(".re__card-location span");
        const location = locationEl.text().trim();

        // Extract posting time
        const timeEl = $element.find(".re__card-published-info-published-at");
        let postedTime = timeEl.first().text().trim();
        // Clean up the posting time text (remove extra whitespace and duplicates)
        if (postedTime) {
          postedTime = postedTime.split("\n")[0].trim();
        }

        if (title && title.length > 10) {
          items.push({
            title: title.trim(),
            url: itemUrl,
            price: price,
            image: imageUrl,
            area: area,
            bedroom: "",
            location: location,
            postedTime: postedTime,
            source: siteName,
            domain: domain,
            extractedAt: new Date().toISOString(),
          });
        }
      });
    } else {
      // Fallback to generic selectors for other sites
      console.log("🔍 Using generic selectors for other sites");

      const selectors = [
        '[data-testid*="listing"]',
        '[class*="listing"]',
        '[class*="ad-item"]',
        '[class*="item-card"]',
        '[class*="property"]',
        "article",
        ".item",
        ".listing",
        ".post",
        ".product",
        ".card",
        '[class*="item"]',
        '[class*="card"]',
      ];

      for (const selector of selectors) {
        const elements = $(selector);
        console.log(
          `   Selector "${selector}": found ${elements.length} elements`
        );

        if (elements.length > 0) {
          elements.each((_, element) => {
            const $element = $(element);

            // Generic title extraction
            let title = "";
            const titleSelectors = [
              "h1",
              "h2",
              "h3",
              "h4",
              ".title",
              '[class*="title"]',
              "a[title]",
              "a",
            ];
            for (const titleSelector of titleSelectors) {
              const titleEl = $element.find(titleSelector).first();
              if (titleEl.length > 0) {
                title = titleEl.attr("title") || titleEl.text().trim();
                if (title && title.length > 5) break;
              }
            }

            // Generic URL extraction
            let itemUrl = "";
            const linkEl = $element.find("a").first();
            if (linkEl.length > 0) {
              itemUrl = linkEl.attr("href");
              if (itemUrl && !itemUrl.startsWith("http")) {
                try {
                  const baseUrl = new URL(url);
                  itemUrl = new URL(itemUrl, baseUrl.origin).href;
                } catch (e) {
                  itemUrl = url;
                }
              }
            }

            // Generic price extraction
            let price = "";
            const priceSelectors = [
              '[class*="price"]',
              'span:contains("₫")',
              'span:contains("triệu")',
              'span:contains("tỷ")',
            ];
            for (const priceSelector of priceSelectors) {
              const priceEl = $element.find(priceSelector).first();
              if (priceEl.length > 0) {
                const priceText = priceEl.text().trim();
                if (
                  priceText &&
                  (priceText.includes("₫") ||
                    priceText.includes("triệu") ||
                    priceText.includes("tỷ"))
                ) {
                  price = priceText;
                  break;
                }
              }
            }

            // Generic image extraction
            let imageUrl = "";
            const imgEl = $element.find("img[src]").first();
            if (imgEl.length > 0) {
              let imgSrc = imgEl.attr("src") || imgEl.attr("data-src");
              if (imgSrc && !imgSrc.startsWith("http")) {
                try {
                  const baseUrl = new URL(url);
                  imageUrl = new URL(imgSrc, baseUrl.origin).href;
                } catch (e) {
                  imageUrl = imgSrc;
                }
              } else {
                imageUrl = imgSrc || "";
              }
            }

            // Generic posting time extraction
            let postedTime = "";
            const timeSelectors = [
              ".re__card-published-info-published-at",
              '[class*="time"]',
              '[class*="date"]',
              '[class*="posted"]',
            ];
            for (const timeSelector of timeSelectors) {
              const timeEl = $element.find(timeSelector).first();
              if (timeEl.length > 0) {
                let timeText = timeEl.text().trim();
                if (
                  timeText &&
                  (timeText.includes("Đăng") ||
                    timeText.includes("ngày") ||
                    timeText.includes("giờ"))
                ) {
                  // Clean up the posting time text
                  postedTime = timeText.split("\n")[0].trim();
                  break;
                }
              }
            }

            if (title && title.length > 10 && title.length < 200) {
              items.push({
                title: title.trim(),
                url: itemUrl || url,
                price: price.trim() || "Giá liên hệ",
                image: imageUrl,
                area: "",
                bedroom: "",
                location: "",
                postedTime: postedTime,
                source: siteName,
                domain: domain,
                extractedAt: new Date().toISOString(),
              });
            }
          });

          if (items.length > 0) {
            console.log(
              `✅ Found ${items.length} items with selector: ${selector}`
            );
            break;
          }
        }
      }
    }

    // Remove duplicates based on title
    const uniqueItems = items
      .filter(
        (item, index, self) =>
          index === self.findIndex((i) => i.title === item.title)
      )
      .slice(0, 50);

    console.log(
      `📄 Extracted ${uniqueItems.length} unique items from ${siteName}`
    );
    return uniqueItems;
  }

  async checkSite(site) {
    logger.monitoring(`[${site.name}] Starting check: ${site.url}`);

    try {
      const html = await this.fetchPage(site.url);
      const currentItems = this.extractItems(html, site.url, site.name);

      logger.info(`📄 [${site.name}] Found ${currentItems.length} total items`);

      if (currentItems.length === 0) {
        logger.warning(
          `[${site.name}] No items found - site structure might have changed`
        );
        // Still send error notification for no items found
        await this.slackNotifier.sendErrorNotification(
          site.name,
          new Error(
            "No items found - website structure may have changed or content is not loading properly"
          )
        );
        return { success: false, error: "No items found" };
      }

      // Check if this is the first run for this site
      const isFirstRun = await this.storage.isFirstRun(site.name);

      if (isFirstRun) {
        logger.info(
          `🎉 [${site.name}] First run - sending all ${currentItems.length} items`
        );

        // Mark all items as seen for future runs
        for (const item of currentItems) {
          await this.storage.addSeenItem(site.name, item);
        }

        // Mark that we've completed the first run
        await this.storage.markFirstRunComplete(site.name);

        // Send all items as "initial items"
        await this.slackNotifier.sendInitialItemsNotification(
          site.name,
          currentItems
        );

        return {
          success: true,
          newItems: currentItems.length,
          totalItems: currentItems.length,
          isFirstRun: true,
        };
      } else {
        // Regular run - check for new items only
        const newItems = [];
        for (const item of currentItems) {
          if (await this.storage.isNewItem(site.name, item)) {
            newItems.push(item);
            await this.storage.addSeenItem(site.name, item);
          }
        }

        if (newItems.length > 0) {
          logger.success(`[${site.name}] Found ${newItems.length} new items`);
          await this.slackNotifier.sendNewItemNotification(site.name, newItems);
          return {
            success: true,
            newItems: newItems.length,
            totalItems: currentItems.length,
          };
        } else {
          logger.info(
            `✅ [${site.name}] No new items (${currentItems.length} total items checked)`
          );
          // Send success notification for completed checks
          await this.slackNotifier.sendSuccessfulCheckNotification(
            site.name,
            currentItems.length
          );
          return {
            success: true,
            newItems: 0,
            totalItems: currentItems.length,
          };
        }
      }
    } catch (error) {
      logger.failure(`[${site.name}] Error during check`, {
        error: error.message,
        stack: error.stack,
      });
      await this.slackNotifier.sendErrorNotification(site.name, error);
      return { success: false, error: error.message };
    }
  }

  async checkAllSites(sites) {
    logger.startup(
      `Starting comprehensive check of ${sites.length} Vietnamese real estate sites...`
    );

    const results = {
      total: sites.length,
      successful: 0,
      failed: 0,
      newItemsFound: 0,
      failedSites: [],
    };

    for (let i = 0; i < sites.length; i++) {
      const site = sites[i];
      logger.info(`📍 [${i + 1}/${sites.length}] Processing ${site.name}...`);

      const result = await this.checkSite(site);

      if (result.success) {
        results.successful++;
        results.newItemsFound += result.newItems || 0;
        logger.success(`[${site.name}] Check completed successfully`);
      } else {
        results.failed++;
        results.failedSites.push({
          name: site.name,
          error: result.error,
        });
        logger.failure(`[${site.name}] Check failed: ${result.error}`);
      }

      // Add delay between requests to be respectful to websites
      if (i < sites.length - 1) {
        const delay = 3000; // 3 seconds between sites for faster monitoring
        logger.info(`⏳ Waiting ${delay / 1000} seconds before next site...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    // Send summary notification
    await this.sendSummaryNotification(results);

    logger.info("📊 Check Summary:", {
      successful: `${results.successful}/${results.total}`,
      failed: `${results.failed}/${results.total}`,
      newItemsFound: results.newItemsFound,
      failedSites:
        results.failedSites.length > 0
          ? results.failedSites.map((s) => s.name).join(", ")
          : "None",
    });
    logger.success("Finished checking all sites");

    return results;
  }

  async sendSummaryNotification(results) {
    try {
      if (results.failed > 0 || results.newItemsFound > 0) {
        const failedSitesList =
          results.failedSites.length > 0
            ? results.failedSites
                .map((site) => `• ${site.name}: ${site.error}`)
                .join("\n")
            : "None";

        const message = {
          text: `📊 Monitoring Summary - ${results.successful}/${results.total} sites successful`,
          blocks: [
            {
              type: "header",
              text: {
                type: "plain_text",
                text: "📊 Vietnamese Real Estate Monitoring Summary",
              },
            },
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*Total Sites Checked:* ${results.total}\n*Successful:* ${results.successful}\n*Failed:* ${results.failed}\n*New Items Found:* ${results.newItemsFound}`,
              },
            },
          ],
        };

        if (results.failedSites.length > 0) {
          message.blocks.push({
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Failed Sites:*\n${failedSitesList}`,
            },
          });
        }

        message.blocks.push({
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `📅 Summary at ${new Date().toLocaleString("vi-VN", {
                timeZone: "Asia/Ho_Chi_Minh",
              })} (Vietnam time)`,
            },
          ],
        });

        await this.slackNotifier.webhook.send(message);
        logger.notification("Summary notification sent to Slack");
      }
    } catch (error) {
      logger.failure("Error sending summary notification", {
        error: error.message,
        stack: error.stack,
      });
    }
  }
}

module.exports = WebsiteMonitor;

// Main execution
async function main() {
  console.log("🚀 Starting Website Monitor...");

  // Load environment variables
  require("dotenv").config();

  console.log("📋 Environment variables loaded:");
  console.log(
    `   SLACK_WEBHOOK_URL: ${process.env.SLACK_WEBHOOK_URL ? "Set" : "Not set"}`
  );
  console.log(
    `   CHECK_INTERVAL_MINUTES: ${
      process.env.CHECK_INTERVAL_MINUTES || "Default (240)"
    }`
  );
  console.log(
    `   USE_BROWSER: ${process.env.USE_BROWSER || "Default (false)"}`
  );

  // Validate required environment variables
  if (!process.env.SLACK_WEBHOOK_URL) {
    console.error("❌ SLACK_WEBHOOK_URL environment variable is required");
    process.exit(1);
  }

  console.log("🔧 Creating monitor instance...");
  const monitor = new WebsiteMonitor(process.env.SLACK_WEBHOOK_URL);

  // Load sites from urls.json
  console.log("📄 Loading sites from urls.json...");
  let sites;
  try {
    const fs = require("fs");
    const urlsData = JSON.parse(fs.readFileSync("./urls.json", "utf8"));
    sites = urlsData.site;
    console.log(`✅ Loaded ${sites.length} site(s) from urls.json`);
    sites.forEach((site, index) => {
      console.log(`   ${index + 1}. ${site.name}: ${site.url}`);
    });
  } catch (error) {
    console.error("❌ Error loading urls.json:", error.message);
    console.log("📄 Using fallback configuration...");
    sites = [
      {
        name: "Batdongsan.com.vn",
        url: "https://batdongsan.com.vn/cho-thue-can-ho-chung-cu-quan-7?gtn=5-trieu&gcn=8-trieu",
      },
    ];
  }

  console.log("▶️ Starting monitor...");

  // Send startup notification
  await monitor.slackNotifier.sendStartupNotification(sites);

  // Run initial check
  console.log("🔍 Running initial check...");
  await monitor.checkAllSites(sites);

  // Set up interval for regular checks
  const intervalMinutes = parseInt(process.env.CHECK_INTERVAL_MINUTES) || 240;
  console.log(
    `⏰ Setting up monitoring interval: every ${intervalMinutes} minutes`
  );

  setInterval(async () => {
    console.log(
      `\n🔄 Starting scheduled check at ${new Date().toLocaleString("vi-VN", {
        timeZone: "Asia/Ho_Chi_Minh",
      })}`
    );
    await monitor.checkAllSites(sites);
  }, intervalMinutes * 60 * 1000);

  console.log("✅ Monitor is running! Press Ctrl+C to stop.");
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log("\n🛑 Received SIGINT, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\n🛑 Received SIGTERM, shutting down gracefully...");
  process.exit(0);
});

// Start the application
if (require.main === module) {
  console.log("🎯 Script started as main module");
  main().catch((error) => {
    console.error("❌ Fatal error:", error);
    process.exit(1);
  });
} else {
  console.log("📦 Script loaded as module");
}
