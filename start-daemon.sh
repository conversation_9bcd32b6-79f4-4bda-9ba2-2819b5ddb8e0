#!/bin/bash

# Simple daemon script to run the website monitor in background
# Alternative to PM2 for simple deployments

APP_NAME="website-monitor"
APP_DIR=$(pwd)
PID_FILE="$APP_DIR/$APP_NAME.pid"
LOG_FILE="$APP_DIR/logs/daemon.log"
ERROR_LOG="$APP_DIR/logs/daemon-error.log"

# Create logs directory if it doesn't exist
mkdir -p logs

start() {
    if [ -f "$PID_FILE" ] && kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        echo "❌ $APP_NAME is already running (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    echo "🚀 Starting $APP_NAME..."

    # Start the application in background
    # Note: Application now uses winston for logging to logs/app-YYYY-MM-DD.log and logs/error-YYYY-MM-DD.log
    # The daemon logs (stdout/stderr) go to separate files for daemon management
    nohup node index.js >> "$LOG_FILE" 2>> "$ERROR_LOG" &

    # Save the PID
    echo $! > "$PID_FILE"

    echo "✅ $APP_NAME started (PID: $(cat $PID_FILE))"
    echo "📄 Application logs: logs/app-$(date +%Y-%m-%d).log"
    echo "📄 Error logs: logs/error-$(date +%Y-%m-%d).log"
    echo "📄 Daemon logs: $LOG_FILE"
    echo "📄 Daemon errors: $ERROR_LOG"
}

stop() {
    if [ ! -f "$PID_FILE" ]; then
        echo "❌ $APP_NAME is not running (no PID file found)"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    
    if ! kill -0 "$PID" 2>/dev/null; then
        echo "❌ $APP_NAME is not running (PID $PID not found)"
        rm -f "$PID_FILE"
        return 1
    fi
    
    echo "🛑 Stopping $APP_NAME (PID: $PID)..."
    kill "$PID"
    
    # Wait for process to stop
    for i in {1..10}; do
        if ! kill -0 "$PID" 2>/dev/null; then
            break
        fi
        sleep 1
    done
    
    # Force kill if still running
    if kill -0 "$PID" 2>/dev/null; then
        echo "⚠️ Force killing $APP_NAME..."
        kill -9 "$PID"
    fi
    
    rm -f "$PID_FILE"
    echo "✅ $APP_NAME stopped"
}

status() {
    if [ -f "$PID_FILE" ] && kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        echo "✅ $APP_NAME is running (PID: $(cat $PID_FILE))"
        return 0
    else
        echo "❌ $APP_NAME is not running"
        return 1
    fi
}

restart() {
    stop
    sleep 2
    start
}

logs() {
    # Show application logs (daily rotating files)
    TODAY=$(date +%Y-%m-%d)
    APP_LOG_TODAY="$APP_DIR/logs/app-$TODAY.log"

    if [ -f "$APP_LOG_TODAY" ]; then
        echo "📄 Showing application logs for today ($TODAY):"
        echo "   Use Ctrl+C to stop following logs"
        echo ""
        tail -f "$APP_LOG_TODAY"
    else
        echo "❌ Today's application log file not found: $APP_LOG_TODAY"
        echo "📁 Available log files:"
        ls -la "$APP_DIR/logs/" | grep "app-.*\.log" || echo "   No application log files found"
    fi
}

errors() {
    # Show error logs (daily rotating files)
    TODAY=$(date +%Y-%m-%d)
    ERROR_LOG_TODAY="$APP_DIR/logs/error-$TODAY.log"

    if [ -f "$ERROR_LOG_TODAY" ]; then
        echo "📄 Showing error logs for today ($TODAY):"
        echo "   Use Ctrl+C to stop following logs"
        echo ""
        tail -f "$ERROR_LOG_TODAY"
    else
        echo "❌ Today's error log file not found: $ERROR_LOG_TODAY"
        echo "📁 Available error log files:"
        ls -la "$APP_DIR/logs/" | grep "error-.*\.log" || echo "   No error log files found"
    fi
}

list_logs() {
    echo "📁 Available log files in logs/ directory:"
    echo ""
    if [ -d "$APP_DIR/logs" ]; then
        ls -lah "$APP_DIR/logs/" | grep -E "\.(log|gz)$" || echo "   No log files found"
    else
        echo "   Logs directory does not exist"
    fi
}

case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    errors)
        errors
        ;;
    list-logs)
        list_logs
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs|errors|list-logs}"
        echo ""
        echo "Commands:"
        echo "  start     - Start the website monitor"
        echo "  stop      - Stop the website monitor"
        echo "  restart   - Restart the website monitor"
        echo "  status    - Check if the monitor is running"
        echo "  logs      - View live application logs (today's file)"
        echo "  errors    - View live error logs (today's file)"
        echo "  list-logs - List all available log files"
        echo ""
        echo "Log files are automatically rotated daily and compressed after 30 days."
        exit 1
        ;;
esac
