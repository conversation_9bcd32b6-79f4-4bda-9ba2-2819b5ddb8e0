# Slack Webhook URL for notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Check interval in minutes (default: 30)
CHECK_INTERVAL_MINUTES=30

# Use browser-based monitoring (true/false) - now runs in headless mode with Cloudflare bypass
USE_BROWSER=false

# Run browser in headless mode (true/false) - headless is invisible and faster
HEADLESS_BROWSER=true

# Log level (debug, info, warn, error)
# Logs are written to both console and daily rotating files in logs/ directory
# Files: app-YYYY-MM-DD.log (all logs), error-YYYY-MM-DD.log (errors only)
# Old log files are automatically compressed and deleted after 30 days
LOG_LEVEL=info
